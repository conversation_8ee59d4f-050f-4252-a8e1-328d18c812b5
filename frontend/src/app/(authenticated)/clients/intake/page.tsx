"use client";

import { useState } from "react";
import { ErrorBoundary } from "react-error-boundary";
import Link from "next/link";
// import { useSupabase } from "@/lib/supabase/provider";
import { CompatCopilotChat } from "@/lib/utils/chat-compat";
import { useCoAgent } from "@copilotkit/react-core";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ProgressWidget } from "@/components/progress/ProgressWidget";
import { StaffIntakeForm } from "@/components/intake/staff-intake-form";

export default function ClientIntakePage() {
  // const router = useRouter();
  // const { supabase } = useSupabase();
  const [mode, setMode] = useState<"ai" | "form">("ai");

  // Use useCoAgent to get the state for the intake_agent
  const { state } = useCoAgent({ name: "intake_agent" });

  const handleError = (error: Error): void => {
    console.error("CopilotKit Error:", error);
  };

  return (
    <div className="container max-w-7xl mx-auto py-8 px-4">
      <div className="mb-8 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            New Client Intake
          </h1>
          <p className="text-muted-foreground mt-1">
            Capture client information for a new personal injury case
          </p>
        </div>
        <Link href="/clients">
          <Button variant="outline">← Back to Clients</Button>
        </Link>
      </div>

      <div className="flex space-x-4 mb-6">
        <Button
          variant={mode === "ai" ? "default" : "outline"}
          onClick={() => setMode("ai")}
          className="flex-1"
        >
          AI Assisted
        </Button>
        <Button
          variant={mode === "form" ? "default" : "outline"}
          onClick={() => setMode("form")}
          className="flex-1"
        >
          Quick Form
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Client Intake Process</CardTitle>
          <CardDescription>
            {mode === "ai"
              ? "Our AI assistant will guide you through collecting client information"
              : "Fill out the required information to begin the client intake process"}
          </CardDescription>
        </CardHeader>

        <CardContent>
          {mode === "ai" ? (
            <div className="flex gap-4">
              {/* Chat interface */}
              <div className="flex-1">
                <ErrorBoundary
                  fallback={<div>Something went wrong</div>}
                  onError={handleError}
                >
                  <CompatCopilotChat
                    className="w-full h-[600px] border rounded-lg bg-background"
                    instructions="You are a legal intake assistant for personal injury cases. You're helping a law firm staff member collect information about a potential client. Be thorough and remember to collect all relevant details for case evaluation. Ask about: client full name, contact details, incident date/location, injury details, other involved parties, insurance info, and any prior legal representation."
                    labels={{
                      title: "Staff Intake Assistant",
                      initial:
                        "Hello! I'll help you collect all the necessary information for this new potential client. Let's start with the client's basic information. What's their full name?",
                    }}
                    agent="intake_agent"
                  />
                </ErrorBoundary>
              </div>

              {/* Progress widget rendered conditionally using the agent state */}
              <div className="w-[300px] shrink-0">
                {state?.show_progress_widget && <ProgressWidget />}
              </div>
            </div>
          ) : (
            <StaffIntakeForm />
          )}
        </CardContent>
      </Card>
    </div>
  );
}
