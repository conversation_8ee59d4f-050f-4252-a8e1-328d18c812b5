import type { ReactNode } from "react";
import { CopilotKit } from "@copilotkit/react-core";
import { getCopilotKitConfig } from "@/lib/env/client";

export default function DocumentsLayout({ children }: { children: ReactNode }) {
  const copilotConfig = getCopilotKitConfig();

  return (
    <CopilotKit
      runtimeUrl="/api/copilotkit/langgraph"
      agent="document_agent" // Lock to document agent
      properties={{
        userRole: "staff",
        context: "documents",
        // In production, use deterministic ID based on user + org
        threadId: `document-${crypto.randomUUID()}`,
      }}
    >
      <div className="documents-layout">{children}</div>
    </CopilotKit>
  );
}
