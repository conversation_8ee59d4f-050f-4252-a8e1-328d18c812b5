import path from "path";
import { fileURLToPath } from "url";
import { createRequire } from "module";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const require = createRequire(import.meta.url);

/** @type {import('next').NextConfig} */
const nextConfig = {
  typescript: {
    // Allow production builds to complete even with type errors
    ignoreBuildErrors: true,
  },
  eslint: {
    // Allow production builds to complete even with ESLint warnings
    ignoreDuringBuilds: true,
    // Only fail on errors, not warnings
    dirs: ["src"],
  },
  experimental: {
    serverActions: {
      bodySizeLimit: "2mb",
      allowedOrigins: ["localhost:3000"],
    },
    optimizePackageImports: ["@copilotkit/react-ui", "@copilotkit/react-core"],
  },
  // Disable source maps in development to avoid file not found errors
  productionBrowserSourceMaps: false,
  // SECURITY: Do NOT expose all environment variables to client-side
  // Only NEXT_PUBLIC_ prefixed variables are automatically exposed
  // Service role keys and other secrets must remain server-side only
  webpack: (config, { isServer }) => {
    // Add webpack plugin to provide exports and module globals for both client and server
    const webpack = require("webpack");
    config.plugins = config.plugins || [];

    // Apply to both client and server bundles to fix module compatibility issues
    config.plugins.push(
      new webpack.DefinePlugin({
        "typeof exports": JSON.stringify("object"),
        "typeof module": JSON.stringify("object"),
      }),
    );

    // Add banner plugin to inject exports definition for both client and server
    config.plugins.push(
      new webpack.BannerPlugin({
        banner:
          'if (typeof exports === "undefined") { var exports = {}; } if (typeof module === "undefined") { var module = { exports: exports }; }',
        raw: true,
        entryOnly: false,
      }),
    );

    // Suppress common warnings
    config.ignoreWarnings = [
      /Critical dependency: the request of a dependency is an expression/,
      /Module not found: Can't resolve 'encoding'/,
    ];

    // Ensure TS path alias '@' resolves to 'src'
    const path = require("path");
    config.resolve = config.resolve || {};
    config.resolve.alias = {
      ...(config.resolve.alias || {}),
      "@": path.resolve(__dirname, "src"),
    };

    return config;
  },
  distDir: ".next",
  async headers() {
    // In development, do not add a global CSP header here to avoid conflicts with middleware and HMR
    if (process.env.NODE_ENV !== "production") {
      return [];
    }
    return [
      {
        source: "/(.*)",
        headers: [
          {
            key: "Content-Security-Policy",
            value: `
              script-src 'self' 'unsafe-eval' 'unsafe-inline'
              https://anwefmklplkjxkmzpnva.supabase.co
              https://challenges.cloudflare.com
              *.fpjs.io *.fpcdn.io fpnpmcdn.net
              https://vercel.live;
              connect-src 'self'
              https://anwefmklplkjxkmzpnva.supabase.co
              wss://anwefmklplkjxkmzpnva.supabase.co
              https://api.cloud.copilotkit.ai
              *.fpjs.io *.fpcdn.io
              https://vercel.live;
            `
              .replace(/\s{2,}/g, " ")
              .trim(),
          },
        ],
      },
    ];
  },
};

export default nextConfig;
