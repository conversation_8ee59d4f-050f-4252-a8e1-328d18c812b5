import type { NextApiRequest, NextApiResponse } from "next";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== "GET") {
    res.setHeader("Allow", "GET");
    res.status(405).json({ ok: false, error: "Method Not Allowed" });
    return;
  }

  const baseUrl = process.env.LANGGRAPH_API_URL;
  if (!baseUrl) {
    res.status(500).json({ ok: false, error: "LANGGRAPH_API_URL is not set" });
    return;
  }
  try {
    const r1 = await fetch(`${baseUrl}/copilotkit/health`);
    if (r1.ok) {
      const data = await r1.json().catch(() => ({}));
      res.status(200).json({ ok: true, upstream: data });
      return;
    }
    const r2 = await fetch(`${baseUrl}/health`);
    const payload = await r2.json().catch(() => ({}));
    res.status(r2.status).json({ ok: r2.ok, upstream: payload });
  } catch (e: any) {
    res.status(502).json({ ok: false, error: e?.message || "Unknown error" });
  }
}

