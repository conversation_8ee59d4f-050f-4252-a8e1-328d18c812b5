"""
Data Residency Middleware for Regional Database Routing

This middleware automatically routes database connections based on user location
and data residency preferences for GDPR/CCPA compliance.
"""

import logging
import time
from typing import Any, Dict, Optional

from fastapi import Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from backend.services.compliance_audit import log_data_residency_event
from backend.services.data_residency import (
    DataRegion,
    get_supabase_client_for_user,
    validate_regional_configuration,
)
from backend.services.location_detection import detect_user_region

logger = logging.getLogger(__name__)


class DataResidencyMiddleware(BaseHTTPMiddleware):
    """
    Middleware to handle data residency routing and compliance.

    This middleware:
    1. Detects user's region based on IP and preferences
    2. Sets up appropriate regional database connections
    3. Logs compliance events for audit trails
    4. Ensures data residency requirements are met
    """

    def __init__(self, app, enable_strict_mode: bool = False):
        super().__init__(app)
        self.enable_strict_mode = enable_strict_mode
        self.regional_config_valid = validate_regional_configuration()

        if not all(self.regional_config_valid.values()):
            logger.warning(
                "Incomplete regional configuration detected",
                extra={"config_status": self.regional_config_valid},
            )

    async def dispatch(self, request: Request, call_next) -> Response:
        """Process request with data residency routing."""
        start_time = time.time()

        try:
            # Skip middleware for health checks and static assets
            if self._should_skip_middleware(request):
                return await call_next(request)

            # Detect user region and set up routing
            await self._setup_regional_routing(request)

            # Process the request
            response = await call_next(request)

            # Log successful request
            await self._log_request_completion(request, response, start_time)

            return response

        except Exception as e:
            logger.error(
                "Data residency middleware error",
                extra={
                    "path": request.url.path,
                    "method": request.method,
                    "error": str(e),
                },
                exc_info=True,
            )

            # In strict mode, return error; otherwise, continue with default routing
            if self.enable_strict_mode:
                return JSONResponse(
                    status_code=500,
                    content={
                        "error": "Data residency routing failed",
                        "message": "Unable to determine appropriate data region",
                    },
                )
            else:
                # Fallback to default routing
                request.state.data_region = DataRegion.US
                request.state.supabase_client = get_supabase_client_for_user(
                    DataRegion.US
                )
                return await call_next(request)

    def _should_skip_middleware(self, request: Request) -> bool:
        """Determine if middleware should be skipped for this request."""
        skip_paths = [
            "/health",
            "/api/health",
            "/copilotkit/health",
            "/api/copilotkit/health",
            "/copilotkit",
            "/api/copilotkit",
            "/metrics",
            "/docs",
            "/openapi.json",
            "/static",
            "/favicon.ico",
            "/.well-known",
        ]

        path = request.url.path.lower()
        return any(path.startswith(skip_path) for skip_path in skip_paths)

    async def _setup_regional_routing(self, request: Request) -> None:
        """Set up regional database routing for the request."""
        # Get user's data residency preference from various sources
        user_preference = await self._get_user_preference(request)

        # Detect user region
        detected_region, region_metadata = await detect_user_region(
            request, user_preference
        )

        # Set up request state with regional information
        request.state.data_region = detected_region
        request.state.region_metadata = region_metadata
        request.state.supabase_client = get_supabase_client_for_user(
            detected_region, user_id=getattr(request.state, "user_id", None)
        )

        # Log the routing decision
        await log_data_residency_event(
            event_type="region_routing",
            user_id=getattr(request.state, "user_id", None),
            region=detected_region.value,
            metadata=region_metadata,
            request_path=request.url.path,
        )

        logger.info(
            f"Routed request to {detected_region.value} region",
            extra={
                "path": request.url.path,
                "region": detected_region.value,
                "method": region_metadata.get("determination_method", []),
            },
        )

    async def _get_user_preference(self, request: Request) -> Optional[str]:
        """Extract user's data residency preference from request."""
        # Check various sources for user preference

        # 1. Query parameter (for testing/debugging)
        if "data_region" in request.query_params:
            return request.query_params["data_region"]

        # 2. Header (for API clients)
        if "X-Data-Region" in request.headers:
            return request.headers["X-Data-Region"]

        # 3. User profile (if authenticated)
        if hasattr(request.state, "user") and request.state.user:
            user = request.state.user
            if hasattr(user, "data_residency_preference"):
                return user.data_residency_preference

        # 4. Session/cookie (for web clients)
        if "data_region_preference" in request.cookies:
            return request.cookies["data_region_preference"]

        return None

    async def _log_request_completion(
        self, request: Request, response: Response, start_time: float
    ) -> None:
        """Log request completion for audit purposes."""
        duration = time.time() - start_time

        # Only log if we have regional routing information
        if hasattr(request.state, "data_region"):
            await log_data_residency_event(
                event_type="request_completed",
                user_id=getattr(request.state, "user_id", None),
                region=request.state.data_region.value,
                metadata={
                    "path": request.url.path,
                    "method": request.method,
                    "status_code": response.status_code,
                    "duration_ms": round(duration * 1000, 2),
                },
            )


class RegionalDatabaseDependency:
    """
    FastAPI dependency for injecting regional database clients.

    This can be used as an alternative to middleware for specific endpoints
    that need explicit regional database access.
    """

    def __init__(self, required_region: Optional[DataRegion] = None):
        self.required_region = required_region

    async def __call__(self, request: Request):
        """Get regional database client for the request."""
        # Use middleware-detected region if available
        if hasattr(request.state, "supabase_client"):
            return request.state.supabase_client

        # Fallback: detect region for this specific request
        user_preference = None
        if hasattr(request.state, "user") and request.state.user:
            user_preference = getattr(
                request.state.user, "data_residency_preference", None
            )

        detected_region, _ = await detect_user_region(request, user_preference)

        # Override with required region if specified
        final_region = self.required_region or detected_region

        return get_supabase_client_for_user(
            final_region, user_id=getattr(request.state, "user_id", None)
        )


# Dependency instances for common use cases
get_regional_db = RegionalDatabaseDependency()
get_us_db = RegionalDatabaseDependency(DataRegion.US)
get_eu_db = RegionalDatabaseDependency(DataRegion.EU)


def get_user_region_from_request(request: Request) -> DataRegion:
    """
    Extract user's data region from request state.

    Args:
        request: FastAPI request object

    Returns:
        User's data region (defaults to US if not set)
    """
    return getattr(request.state, "data_region", DataRegion.US)


def get_region_metadata_from_request(request: Request) -> Dict[str, Any]:
    """
    Extract region detection metadata from request state.

    Args:
        request: FastAPI request object

    Returns:
        Region detection metadata
    """
    return getattr(request.state, "region_metadata", {})
