import type { NextApiRequest, NextApiResponse } from "next";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // CORS preflight
  if (req.method === "OPTIONS") {
    res.setHeader("Access-Control-Allow-Origin", "*");
    res.setHeader(
      "Access-Control-Allow-Headers",
      "Content-Type, Authorization, X-CPK-Endpoint-Key, X-Request-ID"
    );
    res.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
    res.status(200).end();
    return;
  }

  if (req.method !== "POST" && req.method !== "GET") {
    res.setHeader("Allow", "GET, POST, OPTIONS");
    res.status(405).json({ error: "Method Not Allowed" });
    return;
  }

  const baseUrl = process.env.LANGGRAPH_API_URL || "http://localhost:8000";
  const endpoint = "/copilotkit";
  const url = `${baseUrl}${endpoint}`;
  const endpointKey = process.env.CPK_ENDPOINT_SECRET;

  if (!endpointKey) {
    res.status(401).json({ error: "CPK_ENDPOINT_SECRET not set on Vercel" });
    return;
  }

  try {
    // GET streaming proxy
    if (req.method === "GET") {
      const proxied = await fetch(url + (req.url?.includes("?") ? req.url?.slice(req.url.indexOf("?")) : ""), {
        method: "GET",
        headers: { "X-CPK-Endpoint-Key": endpointKey },
      });
      const text = await proxied.text();
      res.status(proxied.status);
      res.setHeader("Content-Type", proxied.headers.get("content-type") || "text/event-stream");
      res.send(text);
      return;
    }

    // POST proxy to FastAPI
    const body = typeof req.body === "string" ? JSON.parse(req.body) : req.body || {};
    const resp = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-CPK-Endpoint-Key": endpointKey,
      },
      body: JSON.stringify(body),
    });

    const contentType = resp.headers.get("content-type") || "application/json";
    const text = await resp.text();
    res.status(resp.status);
    res.setHeader("Content-Type", contentType);
    res.send(text);
  } catch (err: any) {
    res.status(502).json({ error: err?.message || "Upstream error" });
  }
}
