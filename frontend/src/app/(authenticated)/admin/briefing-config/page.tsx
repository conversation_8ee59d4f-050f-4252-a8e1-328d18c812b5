"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, <PERSON>H<PERSON>er, CardTitle } from "../../../../components/ui/card";
import { Button } from "../../../../components/ui/button";
import { Switch } from "../../../../components/ui/switch";
import { Label } from "../../../../components/ui/label";
import { Input } from "../../../../components/ui/input";
import { Badge } from "../../../../components/ui/badge";
import { Alert, AlertDescription } from "../../../../components/ui/alert";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../../components/ui/select";
import { Separator } from "../../../../components/ui/separator";
import { toast } from "sonner";
import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>Tube,
  Save,
  RotateCcw,
  CheckCircle,
  <PERSON><PERSON><PERSON><PERSON>gle,
} from "lucide-react";

interface BriefingSchedule {
  enabled: boolean;
  time: string;
  timezone: string;
  days_of_week: number[];
  include_weekends: boolean;
}

interface TenantBriefingConfig {
  tenant_id: string;
  morning_briefing: BriefingSchedule;
  insight_frequency_hours: number;
  auto_insights_enabled: boolean;
  user_return_threshold_hours: number;
  created_at: string;
  updated_at: string;
}

interface Timezone {
  value: string;
  label: string;
  offset: string;
}

const DAYS_OF_WEEK = [
  { value: 1, label: "Monday", short: "Mon" },
  { value: 2, label: "Tuesday", short: "Tue" },
  { value: 3, label: "Wednesday", short: "Wed" },
  { value: 4, label: "Thursday", short: "Thu" },
  { value: 5, label: "Friday", short: "Fri" },
  { value: 6, label: "Saturday", short: "Sat" },
  { value: 7, label: "Sunday", short: "Sun" },
];

export default function BriefingConfigPage() {
  const [config, setConfig] = useState<TenantBriefingConfig | null>(null);
  const [timezones, setTimezones] = useState<Timezone[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  interface ScheduleTestResults {
    timezone_info?: { current_time?: string; utc_offset?: string };
    next_occurrences?: Array<{
      day_of_week: string;
      local_time: string;
      relative: string;
    }>;
  }
  const [testResults, setTestResults] = useState<ScheduleTestResults | null>(
    null,
  );

  // Fetch current configuration
  const fetchConfig = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/tenant/briefing-config/");
      if (!response.ok) {
        throw new Error("Failed to fetch briefing configuration");
      }
      const data = await response.json();
      setConfig(data);
      setHasChanges(false);
    } catch (error) {
      console.error("Error fetching config:", error);
      toast.error("Failed to load briefing configuration");
    } finally {
      setLoading(false);
    }
  };

  // Fetch supported timezones
  const fetchTimezones = async () => {
    try {
      const response = await fetch("/api/tenant/briefing-config/timezones");
      if (!response.ok) {
        throw new Error("Failed to fetch timezones");
      }
      const data = await response.json();
      setTimezones(data.timezones);
    } catch (error) {
      console.error("Error fetching timezones:", error);
    }
  };

  useEffect(() => {
    fetchConfig();
    fetchTimezones();
  }, []);

  // Track changes
  useEffect(() => {
    setHasChanges(true);
  }, [config]);

  const handleSave = async () => {
    if (!config) return;

    setSaving(true);
    try {
      const response = await fetch("/api/tenant/briefing-config/", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(config),
      });

      if (!response.ok) {
        throw new Error("Failed to save configuration");
      }

      toast.success("Briefing configuration saved successfully");
      setHasChanges(false);
    } catch (error) {
      console.error("Error saving config:", error);
      toast.error("Failed to save briefing configuration");
    } finally {
      setSaving(false);
    }
  };

  const handleTestSchedule = async () => {
    if (!config) return;

    setTesting(true);
    try {
      const response = await fetch(
        "/api/tenant/briefing-config/test-schedule",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(config.morning_briefing),
        },
      );

      if (!response.ok) {
        throw new Error("Failed to test schedule");
      }

      const data = await response.json();
      setTestResults(data);
      toast.success("Schedule test completed");
    } catch (error) {
      console.error("Error testing schedule:", error);
      toast.error("Failed to test schedule");
    } finally {
      setTesting(false);
    }
  };

  const updateConfig = (path: string, value: unknown) => {
    if (!config) return;

    setConfig((prev) => {
      if (!prev) return prev;

      const keys = path.split(".");
      const newConfig: TenantBriefingConfig = { ...prev };
      let current: Record<string, unknown> = newConfig as unknown as Record<
        string,
        unknown
      >;

      for (let i = 0; i < keys.length - 1; i++) {
        const k = keys[i];
        const next = (current[k] as Record<string, unknown>) || {};
        current[k] = { ...next };
        current = current[k] as Record<string, unknown>;
      }

      current[keys[keys.length - 1]] = value as unknown;
      return newConfig as TenantBriefingConfig;
    });
  };

  const toggleDayOfWeek = (day: number) => {
    if (!config) return;

    const currentDays = config.morning_briefing.days_of_week;
    const newDays = currentDays.includes(day)
      ? currentDays.filter((d) => d !== day)
      : [...currentDays, day].sort();

    updateConfig("morning_briefing.days_of_week", newDays);
  };

  if (loading) {
    return (
      <div className="container mx-auto py-6 px-4">
        <div className="space-y-6">
          <div className="h-8 bg-gray-200 rounded animate-pulse" />
          {[1, 2, 3].map((i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div className="h-6 bg-gray-200 rounded animate-pulse" />
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
                  <div className="h-10 bg-gray-200 rounded animate-pulse" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!config) {
    return (
      <div className="container mx-auto py-6 px-4">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Failed to load briefing configuration. Please try refreshing the
            page.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 px-4 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Morning Briefing Configuration</h1>
          <p className="text-muted-foreground">
            Configure when and how morning briefings are delivered to your team
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={handleTestSchedule}
            disabled={testing}
            className="flex items-center gap-2"
          >
            <TestTube className="h-4 w-4" />
            {testing ? "Testing..." : "Test Schedule"}
          </Button>
          {hasChanges && <Badge variant="secondary">Unsaved changes</Badge>}
        </div>
      </div>

      {/* Morning Briefing Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Morning Briefing Schedule
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Enable/Disable */}
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="briefing-enabled">Enable Morning Briefings</Label>
              <p className="text-sm text-muted-foreground">
                Send daily briefings to team members
              </p>
            </div>
            <Switch
              id="briefing-enabled"
              checked={config.morning_briefing.enabled}
              onCheckedChange={(checked) =>
                updateConfig("morning_briefing.enabled", checked)
              }
            />
          </div>

          {config.morning_briefing.enabled && (
            <>
              <Separator />

              {/* Time and Timezone */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="briefing-time">Briefing Time</Label>
                  <Input
                    id="briefing-time"
                    type="time"
                    value={config.morning_briefing.time}
                    onChange={(e) =>
                      updateConfig("morning_briefing.time", e.target.value)
                    }
                    className="mt-2"
                  />
                </div>
                <div>
                  <Label htmlFor="timezone">Timezone</Label>
                  <Select
                    value={config.morning_briefing.timezone}
                    onValueChange={(value) =>
                      updateConfig("morning_briefing.timezone", value)
                    }
                  >
                    <SelectTrigger className="mt-2">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {timezones.map((tz) => (
                        <SelectItem key={tz.value} value={tz.value}>
                          <div className="flex items-center justify-between w-full">
                            <span>{tz.label}</span>
                            <span className="text-xs text-muted-foreground ml-2">
                              {tz.offset}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Days of Week */}
              <div>
                <Label>Briefing Days</Label>
                <p className="text-sm text-muted-foreground mb-3">
                  Select which days of the week to send briefings
                </p>
                <div className="flex flex-wrap gap-2">
                  {DAYS_OF_WEEK.map((day) => (
                    <Button
                      key={day.value}
                      variant={
                        config.morning_briefing.days_of_week.includes(day.value)
                          ? "default"
                          : "outline"
                      }
                      size="sm"
                      onClick={() => toggleDayOfWeek(day.value)}
                      className="min-w-[60px]"
                    >
                      {day.short}
                    </Button>
                  ))}
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Advanced Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Advanced Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="insight-frequency">
                Insight Generation Frequency (hours)
              </Label>
              <Input
                id="insight-frequency"
                type="number"
                min="1"
                max="24"
                value={config.insight_frequency_hours}
                onChange={(e) =>
                  updateConfig(
                    "insight_frequency_hours",
                    parseInt(e.target.value),
                  )
                }
                className="mt-2"
              />
              <p className="text-xs text-muted-foreground mt-1">
                How often to generate new deadline insights (1-24 hours)
              </p>
            </div>
            <div>
              <Label htmlFor="return-threshold">
                User Return Threshold (hours)
              </Label>
              <Input
                id="return-threshold"
                type="number"
                min="1"
                max="48"
                value={config.user_return_threshold_hours}
                onChange={(e) =>
                  updateConfig(
                    "user_return_threshold_hours",
                    parseInt(e.target.value),
                  )
                }
                className="mt-2"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Hours away before triggering return insights (1-48 hours)
              </p>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="auto-insights">Auto-Generate Insights</Label>
              <p className="text-sm text-muted-foreground">
                Automatically generate deadline insights in the background
              </p>
            </div>
            <Switch
              id="auto-insights"
              checked={config.auto_insights_enabled}
              onCheckedChange={(checked) =>
                updateConfig("auto_insights_enabled", checked)
              }
            />
          </div>
        </CardContent>
      </Card>

      {/* Test Results */}
      {testResults && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Schedule Test Results
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <strong>Current Time:</strong>{" "}
                  {testResults.timezone_info?.current_time}
                </div>
                <div>
                  <strong>UTC Offset:</strong>{" "}
                  {testResults.timezone_info?.utc_offset}
                </div>
              </div>

              <div>
                <strong className="text-sm">Next 7 Briefings:</strong>
                <div className="mt-2 space-y-2">
                  {testResults.next_occurrences?.map(
                    (occurrence, index: number) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-2 bg-gray-50 rounded"
                      >
                        <span className="font-medium">
                          {occurrence.day_of_week}
                        </span>
                        <span>{occurrence.local_time}</span>
                        <span className="text-xs text-muted-foreground">
                          {occurrence.relative}
                        </span>
                      </div>
                    ),
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Save Actions */}
      <div className="flex items-center justify-between">
        <Alert className="flex-1 mr-4">
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>
            Changes will take effect on the next scheduled briefing.
            {hasChanges && " You have unsaved changes."}
          </AlertDescription>
        </Alert>

        <div className="flex items-center gap-2">
          {hasChanges && (
            <Button variant="outline" onClick={fetchConfig}>
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
          )}
          <Button
            onClick={handleSave}
            disabled={!hasChanges || saving}
            className="min-w-[120px]"
          >
            <Save className="h-4 w-4 mr-2" />
            {saving ? "Saving..." : "Save Changes"}
          </Button>
        </div>
      </div>
    </div>
  );
}
