"""
Lightweight Research Agent handler for CopilotKit integration.

This module provides a lightweight handler that can be imported quickly
without heavy dependencies, then lazily loads the full research agent
when needed.
"""

import logging
import uuid
from typing import Any, Dict

logger = logging.getLogger(__name__)


class ResearchCopilotHandler:
    """
    Lightweight handler for research agent requests from CopilotKit.
    Uses lazy loading to avoid heavy imports during application startup.
    """

    def __init__(self):
        """Initialize the handler without loading heavy dependencies."""
        self._agent = None
        self._initialized = False

    def _ensure_initialized(self):
        """Lazy initialization of the research agent."""
        if not self._initialized:
            try:
                # Import the actual research agent only when needed
                from backend.agents.interactive.research.agent import ResearchAgent
                from backend.agents.interactive.research.state import (
                    PracticeArea,
                    ResearchState,
                    UserContext,
                )

                self._agent = ResearchAgent()
                self._PracticeArea = PracticeArea
                self._ResearchState = ResearchState
                self._UserContext = UserContext
                self._initialized = True
                logger.info("Research agent initialized successfully")

            except Exception as e:
                logger.error(f"Failed to initialize research agent: {e}")
                # Create a mock agent for graceful degradation
                self._agent = MockResearchAgent()
                self._initialized = True

    async def handle_request(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a CopilotKit request for legal research.

        Args:
            data: Request data from CopilotKit

        Returns:
            Response data in CopilotKit format
        """
        self._ensure_initialized()

        try:
            # Extract user message and any previous history
            messages = data.get("messages", [])
            thread_id = data.get("threadId", str(uuid.uuid4()))

            # Extract the latest user message
            user_message = ""
            for msg in reversed(messages):
                if msg.get("role") == "user":
                    content = msg.get("content", [])
                    if content and isinstance(content, list):
                        user_message = content[0] if isinstance(content[0], str) else ""
                    elif isinstance(content, str):
                        user_message = content
                    break

            if not user_message:
                return {
                    "error": "No user message found in request",
                    "threadId": thread_id,
                }

            # Create user context (simplified for now)
            user_context = {
                "user_id": data.get("userId", "anonymous"),
                "tenant_id": data.get("tenantId", "default"),
                "practice_areas": ["personal_injury"],  # Default practice area
            }

            # If we have a real agent with the expected API, use it.
            # Otherwise, fall back to a no-deps mock to ensure a friendly 200.
            try:
                if hasattr(self._agent, "process_query"):
                    result = await self._agent.process_query(
                        user_message, user_context
                    )
                else:
                    # Always use our lightweight mock for the fallback path
                    result = await MockResearchAgent().mock_process_query(
                        user_message, user_context
                    )
            except Exception as agent_err:
                logger.error(
                    f"Research agent invocation failed, returning mock: {agent_err}"
                )
                result = await MockResearchAgent().mock_process_query(
                    user_message, user_context
                )

            return {
                "response": result.get("response", "Research completed"),
                "citations": result.get("citations", []),
                "threadId": thread_id,
                "status": "success",
            }

        except Exception as e:
            logger.error(f"Error processing research request: {e}")
            return {
                "error": f"Research request failed: {str(e)}",
                "threadId": data.get("threadId", str(uuid.uuid4())),
                "status": "error",
            }


class MockResearchAgent:
    """Mock research agent for graceful degradation when real agent fails to load."""

    async def mock_process_query(
        self, query: str, context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Return a mock response for testing purposes."""
        return {
            "response": f"Mock research response for query: {query}",
            "citations": [
                {
                    "title": "Mock Legal Document",
                    "url": "https://example.com/mock-doc",
                    "snippet": "This is a mock citation for testing purposes.",
                }
            ],
            "status": "mock_response",
        }
