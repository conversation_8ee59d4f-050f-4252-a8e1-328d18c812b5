"use client";

import { CopilotKit } from "@copilotkit/react-core";
import type { ReactNode, ReactElement } from "react";
import { UserActivityProvider } from "@/components/providers/UserActivityProvider";
import { getCopilotKitConfig } from "@/lib/env/client";

export default function DashboardLayout({
  children,
}: {
  children: ReactNode;
}): ReactElement {
  const copilotConfig = getCopilotKitConfig();

  return (
    <UserActivityProvider>
      <CopilotKit
        runtimeUrl="/api/copilotkit/langgraph"
        agent="supervisor_agent"
        properties={{
          userRole: "staff",
          // Deterministic thread ID for tenant isolation
          threadId: crypto.randomUUID(), // In production, use org+user ID hash
        }}
      >
        {children}
      </CopilotKit>
    </UserActivityProvider>
  );
}
